# --- Standard Library Imports ---

# --- Django Imports ---
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


# --- Custom User Management ---

class CustomUserManager(BaseUserManager):
    """Custom user manager using email as unique identifier instead of username"""
    
    def create_user(self, email: str, password: str = None, **extra_fields) -> 'CustomUser':
        """
        Create and save a regular user with the given email and password.
        
        :param email: User's email address
        :param password: User's password
        :param extra_fields: Additional user fields
        :return: CustomUser instance
        :raises ValueError: If email is not provided
        """
        if not email:
            raise ValueError(_('The Email field must be set'))
        
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email: str, password: str = None, **extra_fields) -> 'CustomUser':
        """
        Create and save a superuser with the given email and password.
        
        :param email: Super<PERSON>'s email address
        :param password: Super<PERSON>'s password
        :param extra_fields: Additional superuser fields
        :return: CustomUser instance
        :raises ValueError: If required superuser flags are missing
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if not extra_fields.get('is_staff'):
            raise ValueError(_('Superuser must have is_staff=True.'))
        if not extra_fields.get('is_superuser'):
            raise ValueError(_('Superuser must have is_superuser=True.'))

        return self.create_user(email, password, **extra_fields)


class CustomUser(AbstractUser):
    """Custom authentication model using email as unique identifier with role-based access"""
    # Role constants and choices
    ROLES = (
        ('customer', _('Customer')),
        ('service_provider', _('Service Provider')),
        ('admin', _('Admin')),
    )
    CUSTOMER, SERVICE_PROVIDER, ADMIN = ROLES[0][0], ROLES[1][0], ROLES[2][0]

    # Authentication configuration
    username = None
    email = models.EmailField(_('email address'), unique=True, error_messages={
        'unique': _('A user with this email already exists.')
    })
    role = models.CharField(_('role'), max_length=20, choices=ROLES, default=CUSTOMER)
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    objects = CustomUserManager()

    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        ordering = ['-date_joined']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['role']),
        ]

    def __str__(self) -> str:
        return self.email

    @property
    def full_name(self) -> str:
        return f'{self.first_name} {self.last_name}'.strip()

    @property
    def short_name(self) -> str:
        return self.first_name

    # Dynamic role properties
    def _role_property(role_value):
        @property
        def prop(self):
            return self.role == role_value
        return prop

    # Role properties
    is_customer = _role_property(CUSTOMER)
    is_service_provider = _role_property(SERVICE_PROVIDER)
    is_admin = _role_property(ADMIN)
